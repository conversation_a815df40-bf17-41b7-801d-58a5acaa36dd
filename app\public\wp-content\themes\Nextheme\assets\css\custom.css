/**
 * Onoranze Funebri Ignagni - Landing Page Styles
 * Modern, elegant and professional design
 */

/* ===== IMPORTS & FONTS ===== */
@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://unpkg.com/aos@2.3.1/dist/aos.css');

/* ===== CSS VARIABLES ===== */
:root {
    /* Colors - Onoranze La Serenità "Contrasti Eleganti" Palette */
    --primary-color: #4A4A4A;      /* <PERSON>e <PERSON>do - Headers, footer, main titles */
    --secondary-color: #F2EEE8;    /* Lino Caldo - Content backgrounds */
    --accent-color: #82413F;       /* Bordeaux Terroso - CTA buttons and highlights */
    --accent-light: #8F4A47;       /* Lighter bordeaux for hover states */
    --accent-dark: #75393A;        /* Darker bordeaux for active states */

    --text-primary: #2E2A28;          /* Dark text with warm undertone on light backgrounds */
    --text-secondary: #4A4A4A;        /* Carbone caldo for secondary text */
    --text-light: #999999;            /* Keep original light text */
    --text-white: #FFFFFF;            /* Clean white text on dark backgrounds */

    --bg-primary: #ffffff;            /* Keep white primary background */
    --bg-secondary: #F2EEE8;          /* Lino caldo for content sections */
    --bg-dark: #4A4A4A;               /* Carbone caldo for dark sections */
    --bg-overlay: rgba(74, 74, 74, 0.7);  /* Carbone caldo overlay */

    --border-color: #e5e5e5;
    --border-light: #f0f0f0;

    /* Gradients */
    --gradient-primary: linear-gradient(135deg, #4A4A4A 0%, #5A5A5A 100%);
    --gradient-accent: linear-gradient(135deg, #82413F 0%, #8F4A47 100%);
    --gradient-overlay: linear-gradient(135deg, rgba(74, 74, 74, 0.8) 0%, rgba(90, 90, 90, 0.6) 100%);

    /* Typography */
    --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-heading: 'Playfair Display', Georgia, serif;

    /* Spacing */
    --container-max-width: 1200px;
    --section-padding: 80px 0;
    --element-spacing: 2rem;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 64px rgba(0, 0, 0, 0.25);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 16px;
    --radius-xl: 24px;

    /* Z-index */
    --z-loading: 9999;
    --z-navigation: 1000;
    --z-modal: 1050;
    --z-tooltip: 1100;
}

/* ===== RESET & BASE STYLES ===== */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-size: 1rem;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--bg-primary);
    overflow-x: hidden;
}

body.loading {
    overflow: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 {
    font-size: clamp(2.5rem, 5vw, 4rem);
    font-weight: 700;
}

h2 {
    font-size: clamp(2rem, 4vw, 3rem);
}

h3 {
    font-size: clamp(1.5rem, 3vw, 2rem);
}

h4 {
    font-size: 1.25rem;
}

p {
    margin-bottom: 1rem;
    color: var(--text-secondary);
}

a {
    color: var(--accent-color);
    text-decoration: none;
    transition: var(--transition-fast);
}

a:hover {
    color: var(--accent-dark);
}

/* ===== UTILITY CLASSES ===== */
.container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    color: var(--text-primary);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -0.5rem;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: var(--gradient-accent);
    border-radius: 2px;
}

.section-subtitle {
    font-size: 1.125rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
}

/* ===== BUTTONS ===== */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.875rem 2rem;
    font-family: var(--font-primary);
    font-size: 1.1rem;
    font-weight: 500;
    text-decoration: none;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: var(--transition-slow);
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: var(--gradient-accent);
    color: var(--text-white);
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    color: var(--text-white);
}

.btn-secondary {
    background: transparent;
    color: var(--text-white);
    border: 2px solid var(--text-white);
}

.btn-secondary:hover {
    background: var(--text-white);
    color: var(--text-primary);
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

/* Pulsante diviso in due zone */
.btn-split {
    padding: 0;
    overflow: hidden;
    display: flex;
    align-items: stretch;
    gap: 0;
    border: 5px solid #874442;
}

.btn-main {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.875rem 1.5rem;
    flex: 1;
}

.btn-phone {
    display: flex;
    align-items: center;
    padding: 0.875rem 1rem;
    background: #F2EEE8;
    border-left: 1px solid rgba(255, 255, 255, 0.2);
    font-weight: 700;
    font-size: 1rem;
    white-space: nowrap;
    color: var(--text-primary);
}

.btn-split:hover .btn-phone {
    background: #884543;
    color: var(--text-white);
    border-left: 1px solid #884543; /* Rimuove il divisore bianco */
}

.btn-split:hover .btn-main {
    color: var(--text-white); /* Mantiene visibile "Ceprano" */
}

/* Bordi per i pulsanti nella hero section */
.hero-actions .btn {
    border: 5px solid #874442;
}

.hero-actions .btn-split {
    border: 5px solid #874442; /* Già definito sopra ma per chiarezza */
}

/* Stili specifici per il pulsante secondario diviso */
.btn-secondary.btn-split .btn-phone {
    background: #F2EEE8;
    color: var(--text-primary);
}

.btn-secondary.btn-split:hover .btn-phone {
    background: #884543;
    color: var(--text-white);
    border-left: 1px solid #884543;
}

.btn-secondary.btn-split:hover .btn-main {
    color: var(--text-white);
}

/* ===== LOADING SCREEN ===== */
.loading-screen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-loading);
    transition: opacity var(--transition-slow), visibility var(--transition-slow);
}

.loading-screen.hidden {
    opacity: 0;
    visibility: hidden;
}

.loading-content {
    text-align: center;
    color: var(--text-white);
}

.logo-container {
    margin-bottom: 2rem;
}

.loading-logo {
    width: 120px;
    height: auto;
    opacity: 0;
    animation: fadeInLogo 1s ease forwards;
}

@keyframes fadeInLogo {
    to {
        opacity: 1;
    }
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(122, 139, 115, 0.3);
    border-top: 3px solid var(--accent-color);
    border-radius: 50%;
    margin: 0 auto 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1rem;
    opacity: 0.8;
}

/* ===== SCROLL PROGRESS ===== */
.scroll-progress {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 3px;
    background: rgba(122, 139, 115, 0.2);
    z-index: var(--z-navigation);
}

.progress-bar {
    height: 100%;
    background: var(--gradient-accent);
    width: 0%;
    transition: width 0.1s ease;
}

/* ===== NAVIGATION ===== */
.main-navigation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background: rgba(26, 26, 26, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1001; /* Higher than hero section to overlay properly */
    transition: var(--transition-normal);
    transform: translateY(-100%);
}

.main-navigation.visible {
    transform: translateY(0);
}

.nav-container {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 70px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.nav-logo {
    width: 40px;
    height: auto;
}

.nav-title {
    font-family: var(--font-heading);
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-white);
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    color: var(--text-white);
    font-weight: 500;
    transition: var(--transition-fast);
    position: relative;
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--accent-color);
    transition: var(--transition-fast);
}

.nav-link:hover::after,
.nav-link.active::after {
    width: 100%;
}

.nav-cta {
    background: var(--gradient-accent);
    color: var(--text-white);
    padding: 0.5rem 1rem;
    border-radius: var(--radius-md);
    font-weight: 500;
    transition: var(--transition-normal);
}

.nav-cta:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    color: var(--text-white);
}

.nav-toggle {
    display: none;
    flex-direction: column;
    gap: 4px;
    cursor: pointer;
}

.nav-toggle span {
    width: 25px;
    height: 3px;
    background: var(--text-white);
    transition: var(--transition-fast);
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 70px;
        left: 0;
        width: 100%;
        background: var(--bg-dark);
        flex-direction: column;
        padding: 2rem 0;
        transform: translateX(-100%);
        transition: var(--transition-normal);
    }

    .nav-menu.active {
        transform: translateX(0);
    }

    .nav-toggle {
        display: flex;
    }

    .nav-toggle.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .nav-toggle.active span:nth-child(2) {
        opacity: 0;
    }

    .nav-toggle.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }
}

/* ===== HERO SECTION ===== */
.hero-section {
    position: relative;
    height: 70vh !important;
    max-height: 70vh !important;
    min-height: 70vh !important;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    box-sizing: border-box;
    z-index: 1; /* Lower than header z-index so header overlays */
}

.hero-background-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center center;
    background-repeat: no-repeat;
    animation: heroZoom 20s ease-in-out infinite;
    opacity: 0;
    transition: opacity 2s ease-in-out;
}

.hero-background.active {
    opacity: 1;
}

.hero-background-1 {
    background-image: url('../img/images/impresa_funebre.webp');
}

.hero-background-2 {
    background-image: url('../img/images/Onoranze funebri Ignagni.webp');
}

/* Animazione zoom leggera per lo sfondo hero */
@keyframes heroZoom {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.hero-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(74, 74, 74, 0.6); /* Overlay scuro per leggibilità del testo */
}

.hero-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image:
        radial-gradient(2px 2px at 20px 30px, rgba(122, 139, 115, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(122, 139, 115, 0.2), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(122, 139, 115, 0.4), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(122, 139, 115, 0.3), transparent);
    background-repeat: repeat;
    background-size: 200px 100px;
    animation: particleFloat 20s linear infinite;
}

@keyframes particleFloat {
    0% { transform: translateY(0px); }
    100% { transform: translateY(-200px); }
}

.hero-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 2;
    text-align: center;
    color: var(--text-white);
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding-top: 6vh; /* Offset per compensare il decentramento */
}

.hero-section .container {
    height: 100%;
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-text {
    max-width: 950px; /* Allargato da 800px per ridurre gli a capo */
    width: 100%;
    text-align: center;
    background: rgba(74, 74, 74, 0.4); /* Sfondo semitrasparente scuro */
    backdrop-filter: blur(8px); /* Effetto blur per eleganza */
    -webkit-backdrop-filter: blur(8px); /* Compatibilità Safari */
    border-radius: 16px;
    padding: 2.5rem 2.5rem; /* Padding orizzontale aumentato */
    border: 1px solid rgba(255, 255, 255, 0.1); /* Bordo sottile per definizione */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3); /* Ombra per profondità */
}

/* H1 principale - Tutto il contenuto con bagliore lento */
.hero-tagline {
    font-size: 1.1rem; /* Dimensioni originali del paragrafo */
    color: #FFFFFF; /* Bianco per eleganza */
    font-weight: 700; /* Grassetto per maggiore impatto */
    letter-spacing: 1px;
    text-transform: uppercase;
    margin-bottom: 0.5rem;
    opacity: 0;
    animation: fadeInUp 1s ease 0.3s forwards, blackGlowSlow 8s ease-in-out infinite;
    text-align: center;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.8rem; /* Gap per le decorazioni */
}

.hero-title {
    color: var(--text-white);
    margin-bottom: 1rem;
    opacity: 0;
    animation: fadeInUp 1s ease 0.5s forwards;
    font-size: clamp(1.4rem, 3.8vw, 2.8rem); /* Dimensioni originali dell'H1 */
    line-height: 1.2;
    text-align: center;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
    position: relative;
}

/* Font-size aumentato per notebook e desktop (solo per H2 ex-H1) */
@media (min-width: 992px) {
    .hero-title {
        font-size: clamp(1.6rem, 4.2vw, 3.2rem); /* Aumento leggero su laptop/desktop */
    }
}

@media (min-width: 1200px) {
    .hero-title {
        font-size: clamp(1.8rem, 4.5vw, 3.5rem); /* Aumento maggiore su desktop */
    }

    .hero-text {
        max-width: 1100px; /* Ancora più largo su desktop */
        padding: 2.5rem 3rem; /* Padding orizzontale maggiore su desktop */
    }
}

/* Responsive per laptop */
@media (min-width: 992px) and (max-width: 1199px) {
    .hero-text {
        max-width: 1000px; /* Larghezza intermedia per laptop */
        padding: 2.5rem 2.8rem;
    }
}

/* Animazione bagliore nero molto lento per tutto l'H1 */
@keyframes blackGlowSlow {
    0%, 100% {
        text-shadow: 0 0 5px rgba(0, 0, 0, 0.4);
    }
    50% {
        text-shadow: 0 0 20px rgba(0, 0, 0, 0.8), 0 0 30px rgba(0, 0, 0, 0.6), 0 0 40px rgba(0, 0, 0, 0.4);
    }
}

/* Ornamenti decorativi ai lati dell'H1 */
.hero-tagline::before,
.hero-tagline::after {
    content: '◆';
    color: #FFFFFF; /* Bianco come il testo dell'H1 */
    font-size: 0.8em;
    opacity: 0.8;
    animation: fadeInUp 1s ease 0.5s forwards; /* Solo fadeIn, il bagliore è già applicato all'H1 */
    opacity: 0;
}

.title-line {
    display: block;
    position: relative;
}

.title-line:first-child {
    animation-delay: 0.7s;
}

.title-line:last-child {
    animation-delay: 0.9s;
    color: var(--text-white);
}

/* Rimozione completa effetto separato - tutto gestito dall'H1 */
.company-name-highlight {
    /* Nessun effetto separato - eredita tutto dall'H1 */
}

.hero-subtitle {
    font-size: clamp(0.9rem, 2.5vw, 1.1rem);
    color: rgba(255, 255, 255, 0.9);
    margin-bottom: 1rem;
    opacity: 0;
    animation: fadeInUp 1s ease 1.1s forwards;
    line-height: 1.3;
}

.hero-actions {
    display: flex;
    gap: 0.8rem;
    justify-content: center;
    margin-bottom: 1.2rem;
    opacity: 0;
    animation: fadeInUp 1s ease 1.3s forwards;
}

@media (max-width: 768px) {
    .hero-actions {
        flex-direction: column;
        align-items: center;
    }
}

.hero-features {
    display: flex;
    gap: 1rem;
    justify-content: center;
    opacity: 0;
    animation: fadeInUp 1s ease 1.5s forwards;
    margin-bottom: 0.5rem;
}

.feature-item {
    display: flex;
    align-items: center;
    gap: 0.4rem;
    color: rgba(255, 255, 255, 0.8);
    font-size: clamp(0.7rem, 1.8vw, 0.8rem);
}

.feature-item i {
    color: var(--accent-color);
    font-size: clamp(0.8rem, 1.8vw, 0.9rem);
}

@media (max-width: 768px) {
    .hero-text {
        padding: 2rem 1.5rem; /* Padding ridotto su mobile */
        border-radius: 12px; /* Bordi meno arrotondati su mobile */
        margin: 0 1rem; /* Margini laterali per evitare che tocchi i bordi */
    }

    .hero-tagline {
        font-size: 1rem; /* Font ridotto su mobile (dimensioni originali paragrafo) */
        margin-bottom: 0.4rem;
        letter-spacing: 0.8px;
    }

    .hero-features {
        flex-direction: column;
        gap: 0.6rem;
        margin-bottom: 0.3rem;
    }

    .hero-actions {
        margin-bottom: 1rem;
        gap: 0.6rem;
        flex-direction: column;
        align-items: center;
    }

    .hero-subtitle {
        margin-bottom: 0.8rem;
    }

    .hero-title {
        margin-bottom: 0.6rem;
        font-size: clamp(1.2rem, 5vw, 1.8rem);
        line-height: 1.3;
        max-width: 95%;
        gap: 0.8rem; /* Gap ridotto su mobile */
    }

    .hero-tagline {
        gap: 0.6rem; /* Gap ridotto su mobile per H1 */
    }

    .hero-tagline::before,
    .hero-tagline::after {
        font-size: 0.7em; /* Ornamenti più piccoli su mobile */
    }

    /* Pulsante diviso responsive */
    .btn-split {
        width: 100%;
        max-width: 280px;
    }

    .btn-main {
        padding: 0.75rem 1rem;
    }

    .btn-phone {
        padding: 0.75rem 0.8rem;
        font-size: 0.95rem;
    }
}

/* Extra constraints for very short screens */
@media (max-height: 500px) {
    .hero-title {
        font-size: clamp(1.1rem, 3.5vw, 1.8rem);
        margin-bottom: 0.5rem;
        line-height: 1.3;
    }

    .hero-subtitle {
        font-size: clamp(0.8rem, 2vw, 1rem);
        margin-bottom: 0.8rem;
    }

    .hero-actions {
        margin-bottom: 0.8rem;
    }

    .hero-features {
        margin-bottom: 0.3rem;
    }

    .hero-scroll-indicator {
        bottom: 0.5rem;
        font-size: 0.7rem;
    }
}

/* Extra small mobile devices */
@media (max-width: 480px) {
    .hero-text {
        padding: 1.5rem 1rem; /* Padding ancora più ridotto su schermi piccoli */
        border-radius: 8px; /* Bordi meno arrotondati */
        margin: 0 0.5rem; /* Margini ridotti */
    }

    .hero-title {
        font-size: clamp(1rem, 4.5vw, 1.4rem);
        line-height: 1.4;
        max-width: 98%;
        word-wrap: break-word;
        hyphens: auto;
        gap: 0.6rem; /* Gap ancora più ridotto su schermi piccoli */
    }

    .hero-tagline {
        gap: 0.5rem; /* Gap ancora più ridotto su schermi piccoli per H1 */
    }

    .hero-tagline::before,
    .hero-tagline::after {
        font-size: 0.6em; /* Ornamenti ancora più piccoli */
    }
}

/* ===== HERO VIEWPORT CONSTRAINTS ===== */
/* Hero section always takes full viewport height */
/* Navigation will overlay on top without affecting hero height */

/* Responsive hero heights for different screen sizes */
@media (min-width: 1200px) {
    .hero-section {
        height: 75vh !important;
        max-height: 75vh !important;
        min-height: 75vh !important;
    }
}

@media (max-width: 768px) {
    .hero-section {
        height: 65vh !important;
        max-height: 65vh !important;
        min-height: 65vh !important;
    }
}

/* Only for very short screens, slightly reduce height for usability */
@media (max-height: 500px) {
    .hero-section {
        height: 60vh !important;
        max-height: 60vh !important;
        min-height: 60vh !important;
    }
}

.hero-scroll-indicator {
    position: absolute;
    bottom: 1rem;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    color: rgba(255, 255, 255, 0.7);
    opacity: 0;
    animation: fadeInUp 1s ease 2s forwards, scrollBounce 2s ease-in-out 3s infinite;
    font-size: 0.8rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    z-index: 10;
}

.scroll-mouse {
    width: 24px;
    height: 40px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 12px;
    margin: 0 auto 0.5rem;
    position: relative;
}

.scroll-wheel {
    width: 4px;
    height: 8px;
    background: var(--accent-color);
    border-radius: 2px;
    position: absolute;
    top: 6px;
    left: 50%;
    transform: translateX(-50%);
    animation: scrollWheel 2s ease-in-out infinite;
}

@keyframes scrollWheel {
    0%, 100% { transform: translateX(-50%) translateY(0); opacity: 1; }
    50% { transform: translateX(-50%) translateY(12px); opacity: 0.5; }
}

/* Animazione bounce per l'indicatore di scroll */
@keyframes scrollBounce {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(10px);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ===== WHY CHOOSE US SECTION ===== */
.why-choose-section {
    padding: var(--section-padding);
    background: var(--bg-primary);
    position: relative;
}

.why-choose-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, transparent 49%, rgba(122, 139, 115, 0.02) 50%, transparent 51%);
}

/* Forza il centramento del titolo della sezione Why Choose Us */
section#perche-ci-scelgono .section-header {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    position: relative;
    padding: 2rem 4rem;
    margin-bottom: 4rem;
}

/* Decorazioni laterali - mezzi rettangoli */
section#perche-ci-scelgono .section-header::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 80px;
    background: #864442;
    border-radius: 0 8px 8px 0;
    opacity: 0.8;
}

section#perche-ci-scelgono .section-header::after {
    content: '';
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 6px;
    height: 80px;
    background: #864442;
    border-radius: 8px 0 0 8px;
    opacity: 0.8;
}

section#perche-ci-scelgono .section-title {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
    display: block !important;
}

section#perche-ci-scelgono .section-subtitle {
    text-align: center !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

section.why-choose-section .container .section-header {
    text-align: center !important;
}

section.why-choose-section .container .section-header .section-title {
    text-align: center !important;
}

.reasons-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 2rem;
    position: relative;
    z-index: 1;
}

@media (min-width: 1200px) {
    .reasons-grid {
        gap: 2.5rem;
    }
}

.reason-card {
    background: var(--bg-secondary);
    border-radius: var(--radius-lg);
    padding: 2rem;
    display: flex;
    gap: 1.5rem;
    align-items: flex-start;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
    border-left: 4px solid transparent;
}

/* Decorazioni angolari per le reason cards */
.reason-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 20px;
    height: 20px;
    background: #864442;
    clip-path: polygon(100% 0%, 0% 0%, 100% 100%);
    opacity: 0.8;
    transition: var(--transition-normal);
}

.reason-card::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 20px;
    height: 20px;
    background: #864442;
    clip-path: polygon(0% 100%, 0% 0%, 100% 100%);
    opacity: 0.8;
    z-index: 1;
    transition: var(--transition-normal);
}

.reason-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-left-color: var(--accent-color);
}

.reason-card:hover::after,
.reason-card:hover::before {
    opacity: 1;
    transform: scale(1.1);
}

.reason-card:hover .reason-icon i {
    color: var(--accent-color);
    transform: scale(1.1);
}

.reason-card:hover .reason-title {
    color: var(--accent-dark);
}

.reason-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(122, 139, 115, 0.15) 0%, rgba(122, 139, 115, 0.08) 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
}

.reason-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-accent);
    border-radius: 50%;
    opacity: 0.1;
    z-index: -1;
}

.reason-icon i {
    font-size: 1.5rem;
    color: var(--accent-color);
    transition: var(--transition-normal);
}

.reason-content {
    flex: 1;
}

.reason-title {
    font-size: 1.25rem;
    color: var(--text-primary);
    margin-bottom: 0.75rem;
    font-weight: 600;
    transition: var(--transition-fast);
}

.reason-description {
    color: var(--text-secondary);
    line-height: 1.6;
    margin: 0;
}

.reason-description strong {
    color: var(--accent-color);
    font-weight: 600;
}

@media (max-width: 992px) {
    .reasons-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }
}

@media (max-width: 768px) {
    section#perche-ci-scelgono .section-header {
        padding: 1.5rem 2rem;
    }

    section#perche-ci-scelgono .section-header::before,
    section#perche-ci-scelgono .section-header::after {
        width: 4px;
        height: 60px;
    }

    .reason-card {
        padding: 1.5rem;
        gap: 1rem;
    }

    .reason-icon {
        width: 50px;
        height: 50px;
    }

    .reason-icon i {
        font-size: 1.25rem;
    }

    .reason-title {
        font-size: 1.125rem;
    }
}

/* ===== SERVICES SECTION ===== */
.services-section {
    padding: var(--section-padding);
    background: var(--bg-secondary);
    position: relative;
}

.services-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(45deg, transparent 49%, rgba(122, 139, 115, 0.02) 50%, transparent 51%);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
    position: relative;
    z-index: 1;
}

.service-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 2.5rem 2rem;
    text-align: center;
    box-shadow: var(--shadow-sm);
    transition: var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-accent);
    opacity: 0;
    transition: var(--transition-normal);
    z-index: -1;
}

.service-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-lg);
}

.service-card:hover::before {
    opacity: 0.05;
}

.service-card:hover .service-icon i {
    color: var(--accent-color);
    transform: scale(1.1);
}

.service-card:hover .service-title {
    color: var(--accent-dark);
}

.service-icon {
    width: 80px;
    height: 80px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, rgba(122, 139, 115, 0.1) 0%, rgba(122, 139, 115, 0.05) 100%);
    border-radius: 50%;
    position: relative;
}

.service-icon::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: var(--gradient-accent);
    border-radius: 50%;
    opacity: 0.2;
    z-index: -1;
}

.service-icon i {
    font-size: 2rem;
    color: var(--accent-color);
    transition: var(--transition-normal);
}

.service-title {
    font-size: 1.5rem;
    color: var(--text-primary);
    margin-bottom: 1rem;
    transition: var(--transition-fast);
}

.service-description {
    color: var(--text-secondary);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.service-features {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.service-features span {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: var(--text-light);
    position: relative;
    padding-left: 1rem;
}

.service-features span::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-weight: bold;
}

@media (max-width: 768px) {
    .services-grid {
        grid-template-columns: 1fr;
        gap: 1.5rem;
    }

    .service-card {
        padding: 2rem 1.5rem;
    }
}

/* ===== ABOUT SECTION ===== */
.about-section {
    padding: var(--section-padding);
    background: var(--bg-primary);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text .section-header {
    text-align: left;
    margin-bottom: 2rem;
}

.about-text .section-title::after {
    left: 0;
    transform: none;
}

.about-paragraph {
    font-size: 1.125rem;
    line-height: 1.7;
    margin-bottom: 1.5rem;
}

.about-paragraph strong {
    color: var(--accent-color);
    font-weight: 600;
}

.about-paragraph em {
    color: var(--accent-dark);
    font-style: normal;
    font-weight: 500;
}

.about-values {
    margin-top: 2rem;
}

.value-item {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.value-item:hover {
    background: rgba(122, 139, 115, 0.05);
}

.value-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.value-icon i {
    color: var(--text-white);
    font-size: 1.25rem;
}

.value-content h4 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
    font-size: 1.125rem;
}

.value-content p {
    color: var(--text-secondary);
    font-size: 0.875rem;
    margin: 0;
}

.about-image {
    position: relative;
}

.image-container {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.about-img {
    width: 100%;
    height: 400px;
    object-fit: cover;
    transition: var(--transition-slow);
}

.image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--gradient-overlay);
    display: flex;
    align-items: flex-end;
    padding: 2rem;
    opacity: 0;
    transition: var(--transition-normal);
}

.image-container:hover .image-overlay {
    opacity: 1;
}

.image-container:hover .about-img {
    transform: scale(1.05);
}

.overlay-content h3 {
    color: var(--text-white);
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
}

.overlay-content p {
    color: rgba(255, 255, 255, 0.8);
    margin: 0;
}

@media (max-width: 768px) {
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .about-text .section-header {
        text-align: center;
    }

    .about-text .section-title::after {
        left: 50%;
        transform: translateX(-50%);
    }
}

/* ===== CONTACT SECTION ===== */
.contact-section {
    padding: var(--section-padding);
    background: var(--bg-secondary);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin-bottom: 4rem;
}

.contact-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 2.5rem;
    box-shadow: var(--shadow-md);
    height: fit-content;
}

.contact-header {
    margin-bottom: 2rem;
}

.contact-header h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.contact-header p {
    color: var(--text-secondary);
    margin: 0;
}

.contact-details {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.contact-item {
    display: flex;
    gap: 1rem;
    align-items: flex-start;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.contact-icon i {
    color: var(--text-white);
    font-size: 1.25rem;
}

.contact-text h4 {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.contact-text a {
    color: var(--accent-color);
    font-weight: 500;
    display: block;
    margin-bottom: 0.25rem;
}

.contact-text a:hover {
    color: var(--accent-dark);
}

.contact-note {
    font-size: 0.75rem;
    color: var(--text-light);
    font-style: italic;
}

.opening-hours {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.opening-hours div {
    display: flex;
    justify-content: space-between;
    font-size: 0.875rem;
    color: var(--text-secondary);
}

.emergency-note {
    margin-top: 0.5rem;
    padding: 0.5rem;
    background: rgba(122, 139, 115, 0.1);
    border-radius: var(--radius-sm);
    border-left: 3px solid var(--accent-color);
}

.emergency-note strong {
    color: var(--accent-dark);
    font-size: 0.75rem;
}

/* ===== CONTACT FORM ===== */
.contact-form-card {
    background: var(--bg-primary);
    border-radius: var(--radius-lg);
    padding: 2.5rem;
    box-shadow: var(--shadow-md);
}

.contact-form-card h3 {
    color: var(--text-primary);
    margin-bottom: 0.5rem;
}

.contact-form-card p {
    color: var(--text-secondary);
    margin-bottom: 2rem;
}

.contact-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.875rem 1rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    font-family: var(--font-primary);
    font-size: 1rem;
    transition: var(--transition-fast);
    background: var(--bg-primary);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(122, 139, 115, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
}

.checkbox-group {
    flex-direction: row;
    align-items: flex-start;
    gap: 0.75rem;
}

.checkbox-label {
    display: flex;
    align-items: flex-start;
    gap: 0.5rem;
    cursor: pointer;
    font-size: 0.875rem;
    line-height: 1.4;
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    flex-shrink: 0;
    transition: var(--transition-fast);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--gradient-accent);
    border-color: var(--accent-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: var(--text-white);
    font-size: 0.75rem;
    font-weight: bold;
}

.form-error {
    color: #e74c3c;
    font-size: 0.75rem;
    display: none;
}

.form-group.error input,
.form-group.error select,
.form-group.error textarea {
    border-color: #e74c3c;
}

.form-group.error .form-error {
    display: block;
}

.btn-submit {
    position: relative;
    overflow: hidden;
}

.btn-loading {
    display: none;
}

.btn-submit.loading .btn-text {
    display: none;
}

.btn-submit.loading .btn-loading {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-card,
    .contact-form-card {
        padding: 2rem 1.5rem;
    }
}

/* ===== MAP SECTION ===== */
.map-section {
    margin-top: 4rem;
}

.map-container {
    position: relative;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-lg);
}

.map-overlay {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    padding: 1rem;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-md);
    z-index: 10;
}

.map-info h4 {
    color: var(--text-primary);
    margin-bottom: 0.25rem;
    font-size: 1rem;
}

.map-info p {
    color: var(--text-secondary);
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.map-link {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    color: var(--accent-color);
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition-fast);
}

.map-link:hover {
    color: var(--accent-dark);
}

.map-container iframe {
    width: 100%;
    height: 400px;
    border: none;
}

/* ===== FOOTER ===== */
.main-footer {
    background: var(--gradient-primary);
    color: var(--text-white);
}

.footer-content {
    padding: 4rem 0 2rem;
}

.footer-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 3rem;
}

.footer-section h4 {
    color: var(--text-white);
    margin-bottom: 1.5rem;
    font-size: 1.125rem;
}

.footer-brand {
    max-width: 300px;
}

.footer-logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1rem;
}

.footer-logo-img {
    width: 50px;
    height: auto;
}

.footer-logo h3 {
    color: var(--text-white);
    font-size: 1.25rem;
    margin: 0;
}

.footer-description {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.footer-certifications {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.cert-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
}

.cert-item i {
    color: var(--accent-color);
}

.footer-links {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition-fast);
    font-size: 0.875rem;
}

.footer-links a:hover {
    color: var(--accent-color);
}

.footer-contact {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.footer-contact .contact-item {
    display: flex;
    gap: 0.75rem;
    align-items: flex-start;
}

.footer-contact .contact-item i {
    color: var(--accent-color);
    font-size: 1rem;
    margin-top: 0.125rem;
    flex-shrink: 0;
}

.footer-contact .contact-item div {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.footer-contact strong {
    color: var(--text-white);
    font-size: 0.875rem;
    font-weight: 500;
}

.footer-contact a,
.footer-contact span {
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.875rem;
}

.footer-contact a:hover {
    color: var(--accent-color);
}

.footer-hours {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.hours-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.875rem;
}

.hours-item .day {
    color: rgba(255, 255, 255, 0.8);
}

.hours-item .time {
    color: var(--text-white);
    font-weight: 500;
}

.emergency-hours {
    margin-top: 1rem;
    padding: 0.75rem;
    background: rgba(122, 139, 115, 0.1);
    border-radius: var(--radius-md);
    border-left: 3px solid var(--accent-color);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.emergency-hours i {
    color: var(--accent-color);
    font-size: 0.875rem;
}

.emergency-hours strong {
    color: var(--text-white);
    font-size: 0.75rem;
}

.footer-bottom {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding: 1.5rem 0;
}

.footer-bottom-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-copyright p {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    margin: 0;
}

.footer-legal {
    display: flex;
    gap: 1.5rem;
}

.footer-legal a {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.875rem;
    transition: var(--transition-fast);
}

.footer-legal a:hover {
    color: var(--accent-color);
}

.footer-social {
    display: flex;
    gap: 1rem;
}

.social-link {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgba(255, 255, 255, 0.8);
    transition: var(--transition-normal);
}

.social-link:hover {
    background: var(--accent-color);
    color: var(--text-white);
    transform: translateY(-2px);
}

@media (max-width: 768px) {
    .footer-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .footer-bottom-content {
        flex-direction: column;
        text-align: center;
    }

    .footer-legal {
        order: -1;
    }
}

/* ===== BACK TO TOP BUTTON ===== */
.back-to-top {
    position: fixed;
    bottom: 2rem;
    right: 2rem;
    width: 50px;
    height: 50px;
    background: var(--gradient-accent);
    border: none;
    border-radius: 50%;
    color: var(--text-white);
    font-size: 1.25rem;
    cursor: pointer;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    z-index: 100;
    opacity: 0;
    visibility: hidden;
    transform: translateY(20px);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.back-to-top:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-xl);
}

/* ===== EMERGENCY CONTACT BUTTON ===== */
.emergency-contact {
    position: fixed;
    bottom: 2rem;
    left: 2rem;
    z-index: 100;
}

.emergency-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    background: #e74c3c;
    color: var(--text-white);
    padding: 1rem 1.5rem;
    border-radius: 50px;
    text-decoration: none;
    box-shadow: var(--shadow-lg);
    transition: var(--transition-normal);
    animation: emergencyPulse 2s ease-in-out infinite;
}

.emergency-btn:hover {
    background: #c0392b;
    color: var(--text-white);
    transform: translateY(-2px);
    box-shadow: var(--shadow-xl);
    animation: none;
}

.emergency-btn i {
    font-size: 1.25rem;
}

.emergency-text {
    font-weight: 600;
    font-size: 0.875rem;
}

@keyframes emergencyPulse {
    0%, 100% { box-shadow: var(--shadow-lg); }
    50% { box-shadow: 0 8px 32px rgba(231, 76, 60, 0.4); }
}

@media (max-width: 768px) {
    .emergency-contact {
        bottom: 5rem;
    }

    .emergency-btn {
        padding: 0.875rem 1.25rem;
    }

    .emergency-text {
        display: none;
    }
}

/* ===== COOKIE BANNER ===== */
.cookie-banner {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background: var(--bg-dark);
    color: var(--text-white);
    padding: 1.5rem 0;
    z-index: var(--z-modal);
    transform: translateY(100%);
    transition: var(--transition-normal);
}

.cookie-banner.visible {
    transform: translateY(0);
}

.cookie-content {
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 2rem;
}

.cookie-text p {
    color: rgba(255, 255, 255, 0.9);
    margin: 0;
    font-size: 0.875rem;
}

.cookie-actions {
    display: flex;
    gap: 1rem;
    flex-shrink: 0;
}

@media (max-width: 768px) {
    .cookie-content {
        flex-direction: column;
        gap: 1rem;
    }

    .cookie-actions {
        width: 100%;
        justify-content: center;
    }
}

/* ===== GLASSMORPHISM EFFECTS ===== */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* ===== ANIMATIONS ===== */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* ===== ACCESSIBILITY ===== */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* ===== PRINT STYLES ===== */
@media print {
    .loading-screen,
    .scroll-progress,
    .main-navigation,
    .hero-scroll-indicator,
    .back-to-top,
    .emergency-contact,
    .cookie-banner {
        display: none !important;
    }

    .hero-section {
        min-height: auto;
        padding: 2rem 0;
    }

    .hero-background {
        display: none;
    }

    .hero-content {
        color: var(--text-primary);
    }

    * {
        box-shadow: none !important;
    }
}
